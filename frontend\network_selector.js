/**
 * 前端路网选择器模块
 * 用于在主页面中进行出入口、限行道路、交叉口、分析路段的选择
 */

class FrontendNetworkSelector {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.intersections = [];
        this.edges = [];
        this.bounds = {};
        this.selectedIntersectionIds = new Set();
        this.selectedEdgeIds = new Set();
        
        // 视图控制
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.baseScale = 1;
        this.baseOffsetX = 0;
        this.baseOffsetY = 0;
        
        // 拖拽控制
        this.isDragging = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.dragStartOffsetX = 0;
        this.dragStartOffsetY = 0;
        
        // 当前选择器类型和回调
        this.currentMode = null; // 'entrance', 'restriction', 'intersection', 'analysis'
        this.onSelectionComplete = null;
        this.allowIntersectionSelection = false;
        this.allowEdgeSelection = false;
        
        // 道路名称显示控制
        this.showRoadNames = false;

        // 大小变化监听器（用于嵌入模式）
        this.resizeObserver = null;

        this.isVisible = false;
    }

    /**
     * 解析SUMO路网XML文件
     */
    async parseNetworkFile(filePath) {
        try {
            console.log('开始解析路网文件:', filePath);
            
            let xmlText;
            
            // 检查是否为自定义文件
            if (filePath.startsWith('CUSTOM_FILE:')) {
                // 使用本地文件内容
                if (window.uploadedNetFileContent) {
                    xmlText = window.uploadedNetFileContent;
                    console.log('使用本地文件内容');
                } else {
                    throw new Error('自定义文件内容未加载，请重新选择文件');
                }
            } else {
                // 通过后端API获取预设文件内容
                const response = await fetch('http://localhost:8888/api/get_network_file', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ file_path: filePath })
                });
                
                if (!response.ok) {
                    throw new Error(`无法获取路网文件: ${response.status}`);
                }
                
                xmlText = await response.text();
            }
            
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
            
            // 检查XML解析是否成功
            const parseError = xmlDoc.querySelector('parsererror');
            if (parseError) {
                throw new Error('路网文件格式错误: ' + parseError.textContent);
            }
            
            this.parseNetworkXML(xmlDoc);
            console.log(`成功解析到 ${this.intersections.length} 个交叉口, ${this.edges.length} 条路段`);
            
        } catch (error) {
            console.error('解析路网文件失败:', error);
            throw error;
        }
    }

    /**
     * 解析路网XML文档
     */
    parseNetworkXML(xmlDoc) {
        this.intersections = [];
        this.edges = [];
        
        // 解析交叉口
        const junctions = xmlDoc.querySelectorAll('junction[type="traffic_light"]');
        junctions.forEach(junction => {
            const intersection = {
                id: junction.getAttribute('id'),
                x: parseFloat(junction.getAttribute('x')),
                y: parseFloat(junction.getAttribute('y')),
                selected: false
            };
            this.intersections.push(intersection);
        });
        
        // 解析路段
        const edges = xmlDoc.querySelectorAll('edge');
        edges.forEach(edge => {
            const edgeId = edge.getAttribute('id');
            // 跳过内部路段（以冒号开头）
            if (edgeId && !edgeId.startsWith(':')) {
                const fromJunction = edge.getAttribute('from');
                const toJunction = edge.getAttribute('to');
                const edgeName = edge.getAttribute('name') || '';
                
                // 获取路段的几何形状
                const lanes = edge.querySelectorAll('lane');
                if (lanes.length > 0) {
                    const firstLane = lanes[0];
                    const shape = firstLane.getAttribute('shape');
                    
                    if (shape) {
                        const points = [];
                        const pointStrings = shape.split(' ');
                        
                        pointStrings.forEach(pointStr => {
                            const coords = pointStr.split(',');
                            if (coords.length === 2) {
                                const x = parseFloat(coords[0]);
                                const y = parseFloat(coords[1]);
                                if (!isNaN(x) && !isNaN(y)) {
                                    points.push({ x, y });
                                }
                            }
                        });
                        
                        if (points.length >= 2) {
                            const edgeInfo = {
                                id: edgeId,
                                name: edgeName,
                                from: fromJunction,
                                to: toJunction,
                                points: points,
                                selected: false
                            };
                            this.edges.push(edgeInfo);
                        }
                    }
                }
            }
        });
        
        // 计算边界框
        this.calculateBounds();
    }

    /**
     * 计算路网的边界框
     */
    calculateBounds() {
        const allPoints = [];
        
        this.intersections.forEach(intersection => {
            allPoints.push({ x: intersection.x, y: intersection.y });
        });
        
        this.edges.forEach(edge => {
            edge.points.forEach(point => {
                allPoints.push(point);
            });
        });
        
        if (allPoints.length > 0) {
            const xCoords = allPoints.map(p => p.x);
            const yCoords = allPoints.map(p => p.y);
            
            this.bounds = {
                min_x: Math.min(...xCoords),
                max_x: Math.max(...xCoords),
                min_y: Math.min(...yCoords),
                max_y: Math.max(...yCoords)
            };
        } else {
            this.bounds = {};
        }
    }

    /**
     * 显示选择器
     */
    // 支持将选择器嵌入到指定父节点(parentElement)中，而非总是全屏弹窗
    showSelector(mode, onComplete, parentElement = null) {
        this.currentMode = mode;
        this.onSelectionComplete = onComplete;
        // 如果提供了父容器则视为嵌入模式
        this.embedParent = parentElement;
        this.isEmbedded = !!parentElement;
        
        // 根据模式设置选择权限
        switch (mode) {
            case 'entrance':
            case 'restriction':
            case 'analysis':
                this.allowIntersectionSelection = false;
                this.allowEdgeSelection = true;
                break;
            case 'intersection':
                this.allowIntersectionSelection = true;
                this.allowEdgeSelection = false; // 交叉口优化选择器只支持交叉口选择
                break;
        }
        
        // 清空之前的选择
        this.selectedIntersectionIds.clear();
        this.selectedEdgeIds.clear();
        this.intersections.forEach(i => i.selected = false);
        this.edges.forEach(e => e.selected = false);
        
        // 显示选择器UI
        this.createSelectorUI();
        this.isVisible = true;
    }

    /**
     * 创建选择器UI
     */
    createSelectorUI() {
        // 如果是嵌入模式，优先清理父容器已有内容
        let selectorContainer;
        if (this.isEmbedded && this.embedParent) {
            this.embedParent.innerHTML = '';
            selectorContainer = document.createElement('div');
            selectorContainer.id = 'network-selector-container';
            selectorContainer.className = 'network-selector-embedded';
            this.embedParent.appendChild(selectorContainer);
        } else {
            // 检查是否已存在全屏选择器容器
            const existed = document.getElementById('network-selector-container');
            if (existed) existed.remove();

            selectorContainer = document.createElement('div');
            selectorContainer.id = 'network-selector-container';
            selectorContainer.className = 'network-selector-overlay';
            document.body.appendChild(selectorContainer);
        }
        
        const modeNames = {
            'entrance': '出入口路段选择器',
            'restriction': '道路限行选择器',
            'intersection': '交叉口优化选择器',
            'analysis': '自定义路段指标评价选择器'
        };
        
        selectorContainer.innerHTML = `
            <div class="network-selector-modal">
                <div class="selector-header">
                    <h3>${modeNames[this.currentMode] || '网络选择器'}</h3>
                    <button class="close-btn" onclick="networkSelector.hideSelector()">&times;</button>
                </div>
                <div class="selector-controls">
                    <div class="info">
                        <span id="selector-total-count">正在加载...</span>
                        <span id="selector-selected-count">已选择: 0</span>
                    </div>
                    <div class="buttons">
                        <button id="selector-toggle-names">显示道路名称</button>
                        <button id="selector-select-all">全选</button>
                        <button id="selector-clear-all">清空</button>
                        <button id="selector-confirm" disabled>确认选择</button>
                    </div>
                </div>
                <div class="selector-main">
                    <div class="map-container">
                        <canvas id="network-selector-canvas"></canvas>
                        <div class="zoom-controls">
                            <button class="zoom-btn" id="selector-zoom-in">+</button>
                            <button class="zoom-btn" id="selector-zoom-out">−</button>
                            <button class="zoom-btn" id="selector-zoom-reset">⌂</button>
                        </div>
                    </div>
                    <div class="selector-sidebar">
                        <h4>选中的元素</h4>
                        <div id="selector-selected-list" class="selected-list">
                            <p class="empty-message">请在地图上点击选择元素</p>
                        </div>
                        <div class="instructions">
                            <h5>使用说明:</h5>
                            <ul>
                                <li>点击地图上的元素进行选择</li>
                                <li>再次点击可取消选择</li>
                                <li>使用鼠标滚轮缩放</li>
                                <li>拖拽地图进行平移</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 如果是嵌入模式，selectorContainer 已经被插入到 embedParent；
        // 全屏模式已在上面插入到 body。
        
        // 初始化canvas
        this.initializeCanvas();
        
        // 设置事件监听器
        this.setupSelectorEventListeners();
        
        // 更新显示信息
        this.updateSelectorDisplay();
        
        // 绘制网络
        this.resizeCanvas(); // 先调整canvas尺寸
        this.fitToCanvas();  // 再适配视图
        this.drawNetwork();
    }

    /**
     * 初始化canvas
     */
    initializeCanvas() {
        this.canvas = document.getElementById('network-selector-canvas');
        this.ctx = this.canvas.getContext('2d');
        
        // 设置canvas尺寸
        this.resizeCanvas();
    }

    /**
     * 设置选择器事件监听器
     */
    setupSelectorEventListeners() {
        // 按钮事件
        document.getElementById('selector-toggle-names').addEventListener('click', () => this.toggleRoadNames());
        document.getElementById('selector-select-all').addEventListener('click', () => this.selectAll());
        document.getElementById('selector-clear-all').addEventListener('click', () => this.clearAll());
        document.getElementById('selector-confirm').addEventListener('click', () => this.confirmSelection());
        
        // 缩放按钮
        document.getElementById('selector-zoom-in').addEventListener('click', () => this.zoomAtCenter(1.2));
        document.getElementById('selector-zoom-out').addEventListener('click', () => this.zoomAtCenter(0.8));
        document.getElementById('selector-zoom-reset').addEventListener('click', () => this.resetZoom());
        
        // Canvas事件
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('wheel', (e) => this.handleWheel(e));
        
        // 全局事件（处理拖拽）
        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                this.handleGlobalMouseMove(e);
            }
        });
        
        document.addEventListener('mouseup', (e) => {
            if (this.isDragging) {
                this.handleMouseUp(e);
            }
        });

        // 窗口大小变化监听器（用于嵌入模式下的自适应）
        if (this.isEmbedded) {
            this.resizeObserver = new ResizeObserver(() => {
                // 延迟执行以确保DOM更新完成
                setTimeout(() => {
                    this.resizeCanvas();
                }, 100);
            });

            // 观察嵌入容器的大小变化
            if (this.embedParent) {
                this.resizeObserver.observe(this.embedParent);
            }
        }
    }

    /**
     * 更新选择器显示信息
     */
    updateSelectorDisplay() {
        const totalCountElement = document.getElementById('selector-total-count');
        const selectedCountElement = document.getElementById('selector-selected-count');
        
        if (this.allowIntersectionSelection && this.allowEdgeSelection) {
            // 交叉口优化选择器：同时显示交叉口和路段
            totalCountElement.textContent = `交叉口: ${this.intersections.length} | 路段: ${this.edges.length}`;
            selectedCountElement.textContent = `已选择: 交叉口 ${this.selectedIntersectionIds.size} 个, 路段 ${this.selectedEdgeIds.size} 条`;
        } else if (this.allowIntersectionSelection) {
            totalCountElement.textContent = `交叉口: ${this.intersections.length}`;
            selectedCountElement.textContent = `已选择: ${this.selectedIntersectionIds.size}`;
        } else if (this.allowEdgeSelection) {
            totalCountElement.textContent = `路段: ${this.edges.length}`;
            selectedCountElement.textContent = `已选择: ${this.selectedEdgeIds.size}`;
        }
        
        // 更新确认按钮状态
        const confirmBtn = document.getElementById('selector-confirm');
        const hasSelection = this.selectedIntersectionIds.size > 0 || this.selectedEdgeIds.size > 0;
        confirmBtn.disabled = !hasSelection;
        
        // 更新选中列表
        this.updateSelectedList();
    }

    /**
     * 更新选中元素列表
     */
    updateSelectedList() {
        const listElement = document.getElementById('selector-selected-list');
        let html = '';
        
        if (this.allowIntersectionSelection && this.selectedIntersectionIds.size > 0) {
            html += '<h6>选中的交叉口:</h6><ul>';
            this.selectedIntersectionIds.forEach(id => {
                html += `<li>${id}</li>`;
            });
            html += '</ul>';
        }
        
        if (this.allowEdgeSelection && this.selectedEdgeIds.size > 0) {
            html += '<h6>选中的路段:</h6><ul>';
            this.selectedEdgeIds.forEach(id => {
                const edge = this.edges.find(e => e.id === id);
                const name = edge && edge.name ? ` (${edge.name})` : '';
                html += `<li>${id}${name}</li>`;
            });
            html += '</ul>';
        }
        
        if (html === '') {
            html = '<p class="empty-message">请在地图上点击选择元素</p>';
        }
        
        listElement.innerHTML = html;
    }

    /**
     * 隐藏选择器
     */
    hideSelector() {
        // 如果是嵌入模式，不移除DOM，而是恢复默认地图视图
        if (this.isEmbedded && this.embedParent) {
            // 隐藏选择器视图
            this.embedParent.style.display = 'none';

            // 恢复默认地图视图
            const defaultMapView = document.getElementById('default-map-view');
            if (defaultMapView) {
                defaultMapView.style.display = 'flex';

                // 检查是否需要重新显示地图
                const mapPlaceholder = defaultMapView.querySelector('.map-placeholder');
                if (mapPlaceholder && this.edges && this.edges.length > 0) {
                    // 如果有路网数据，尝试显示普通地图
                    setTimeout(() => {
                        this.showNormalMapInDefaultView();
                    }, 100);
                }
            }
        } else {
            // 全屏模式移除整个DOM
            const container = document.getElementById('network-selector-container');
            if (container) {
                container.remove();
            }
        }

        this.isVisible = false;

        // 清理ResizeObserver
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }

        // 从窗口中移除全局事件监听器
        window.removeEventListener('mousemove', this.boundHandleGlobalMouseMove);
        window.removeEventListener('mouseup', this.boundHandleMouseUp);
    }

    /**
     * 在默认视图中显示普通地图（避免重复刷新）
     */
    showNormalMapInDefaultView() {
        try {
            // 如果有活跃的仿真，不显示普通地图
            if (this.isSimulationActive()) {
                console.log('仿真正在运行，跳过普通地图显示');
                return;
            }

            // 获取默认地图视图容器
            const defaultMapView = document.getElementById('default-map-view');
            if (!defaultMapView) return;

            // 检查是否已经有地图内容
            const existingCanvas = defaultMapView.querySelector('canvas');
            if (existingCanvas) {
                console.log('地图已存在，跳过重复加载');
                return;
            }

            // 获取当前路网文件路径
            const getCurrentNetFilePath = window.getCurrentNetFilePath;
            if (typeof getCurrentNetFilePath === 'function') {
                const netFilePath = getCurrentNetFilePath();
                if (netFilePath) {
                    // 显示普通地图，但不重复刷新
                    this.showNormalMap(netFilePath);
                }
            }
        } catch (error) {
            console.error('在默认视图中显示地图失败:', error);
        }
    }

    /**
     * 确认选择
     */
    confirmSelection() {
        const selectedIds = [];
        
        // 收集选中的交叉口ID
        if (this.allowIntersectionSelection) {
            const selectedIntersections = this.intersections.filter(i => i.selected).map(i => i.id);
            
            if (this.currentMode === 'intersection') {
                // 如果是交叉口模式，并且有混合选择，返回特殊结构
                if (this.allowEdgeSelection && this.selectedEdgeIds.size > 0) {
                    const selectedEdges = Array.from(this.selectedEdgeIds);
                    this.onSelectionComplete?.({
                        intersections: selectedIntersections,
                        edges: selectedEdges
                    });
                } else {
                    this.onSelectionComplete?.(selectedIntersections);
                }
            } else {
                // 其他模式，每种类型分开选择
                this.onSelectionComplete?.(selectedIntersections);
            }
        } 
        // 收集选中的路段ID
        else if (this.allowEdgeSelection) {
            const selectedEdges = Array.from(this.selectedEdgeIds);
            this.onSelectionComplete?.(selectedEdges);
        }

        // 隐藏选择器
        this.hideSelector();
    }

    /**
     * 全选
     */
    selectAll() {
        if (this.allowIntersectionSelection) {
            this.intersections.forEach(intersection => {
                intersection.selected = true;
                this.selectedIntersectionIds.add(intersection.id);
            });
        }
        
        if (this.allowEdgeSelection) {
            this.edges.forEach(edge => {
                edge.selected = true;
                this.selectedEdgeIds.add(edge.id);
            });
        }
        
        this.drawNetwork();
        this.updateSelectorDisplay();
    }

    /**
     * 清空选择
     */
    clearAll() {
        this.selectedIntersectionIds.clear();
        this.selectedEdgeIds.clear();
        this.intersections.forEach(i => i.selected = false);
        this.edges.forEach(e => e.selected = false);
        
        this.drawNetwork();
        this.updateSelectorDisplay();
    }

    /**
     * 切换道路名称显示
     */
    toggleRoadNames() {
        this.showRoadNames = !this.showRoadNames;
        const button = document.getElementById('selector-toggle-names');
        if (button) {
            button.textContent = this.showRoadNames ? '隐藏道路名称' : '显示道路名称';
        }
        this.drawNetwork();
    }

    // 视图控制方法
    resizeCanvas() {
        if (!this.canvas) return;
        
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        // 确保获取到有效的尺寸
        const width = rect.width > 0 ? rect.width : 800;
        const height = rect.height > 0 ? rect.height : 600;
        
        this.canvas.width = width;
        this.canvas.height = height;
        
        console.log(`Canvas 尺寸: ${this.canvas.width} x ${this.canvas.height}`);
        
        // 如果已有数据且不是首次初始化，重新适配视图
        if (this.intersections.length > 0 && this.baseScale > 0) {
            this.fitToCanvas();
            this.drawNetwork();
        }
    }

    fitToCanvas() {
        if (!this.bounds || Object.keys(this.bounds).length === 0) return;

        const margin = 50;
        const canvasWidth = this.canvas.width - 2 * margin;
        const canvasHeight = this.canvas.height - 2 * margin;
        
        const dataWidth = this.bounds.max_x - this.bounds.min_x;
        const dataHeight = this.bounds.max_y - this.bounds.min_y;
        
        if (dataWidth === 0 || dataHeight === 0) return;
        
        const scaleX = canvasWidth / dataWidth;
        const scaleY = canvasHeight / dataHeight;
        this.baseScale = Math.min(scaleX, scaleY) * 0.9;
        
        const centerX = (this.bounds.min_x + this.bounds.max_x) / 2;
        const centerY = (this.bounds.min_y + this.bounds.max_y) / 2;
        
        this.baseOffsetX = this.canvas.width / 2 - centerX * this.baseScale;
        // Y坐标翻转后的中心计算 - 修复初始位置
        this.baseOffsetY = this.canvas.height / 2 - centerY * this.baseScale;
        
        this.resetZoom();
    }

    resetZoom() {
        this.scale = this.baseScale;
        this.offsetX = this.baseOffsetX;
        this.offsetY = this.baseOffsetY;
        this.drawNetwork();
    }

    zoomAtCenter(factor) {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        const worldCenter = this.screenToWorld(centerX, centerY);
        
        this.scale *= factor;
        this.scale = Math.max(this.baseScale * 0.1, Math.min(this.scale, this.baseScale * 50));
        
        const newScreenCenter = this.worldToScreen(worldCenter.x, worldCenter.y);
        this.offsetX += centerX - newScreenCenter.x;
        this.offsetY -= centerY - newScreenCenter.y; // 修复Y方向缩放偏移
        
        this.drawNetwork();
    }

    worldToScreen(worldX, worldY) {
        return {
            x: worldX * this.scale + this.offsetX,
            y: this.canvas.height - (worldY * this.scale + this.offsetY)
        };
    }

    screenToWorld(screenX, screenY) {
        return {
            x: (screenX - this.offsetX) / this.scale,
            y: (this.canvas.height - screenY - this.offsetY) / this.scale
        };
    }

    // 鼠标事件处理
    getMousePos(event) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
    }

    handleMouseDown(event) {
        event.preventDefault();
        const mousePos = this.getMousePos(event);
        
        if (event.button === 0) { // 左键
            // 检查是否点击到了元素
            let clicked = false;
            
            if (this.allowIntersectionSelection) {
                const intersection = this.getIntersectionAt(mousePos.x, mousePos.y);
                if (intersection) {
                    this.toggleIntersectionSelection(intersection.id);
                    clicked = true;
                }
            }
            
            if (!clicked && this.allowEdgeSelection) {
                const edge = this.getEdgeAt(mousePos.x, mousePos.y);
                if (edge) {
                    this.toggleEdgeSelection(edge.id);
                    clicked = true;
                }
            }
            
            if (!clicked) {
                // 开始拖拽
                this.isDragging = true;
                this.dragStartX = mousePos.x;
                this.dragStartY = mousePos.y;
                this.dragStartOffsetX = this.offsetX;
                this.dragStartOffsetY = this.offsetY;
            }
        }
    }

    handleMouseMove(event) {
        if (!this.isDragging) return;
        event.preventDefault();
        
        const mousePos = this.getMousePos(event);
        this.offsetX = this.dragStartOffsetX + (mousePos.x - this.dragStartX);
        this.offsetY = this.dragStartOffsetY - (mousePos.y - this.dragStartY); // 修复Y方向拖动
        
        this.drawNetwork();
    }

    handleGlobalMouseMove(event) {
        if (!this.isDragging) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const mousePos = {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
        
        this.offsetX = this.dragStartOffsetX + (mousePos.x - this.dragStartX);
        this.offsetY = this.dragStartOffsetY - (mousePos.y - this.dragStartY); // 修复Y方向拖动
        
        this.drawNetwork();
    }

    handleMouseUp(event) {
        this.isDragging = false;
    }

    handleWheel(event) {
        event.preventDefault();
        
        const mousePos = this.getMousePos(event);
        const worldPos = this.screenToWorld(mousePos.x, mousePos.y);
        
        const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
        this.scale *= zoomFactor;
        this.scale = Math.max(this.baseScale * 0.1, Math.min(this.scale, this.baseScale * 50));
        
        const newScreenPos = this.worldToScreen(worldPos.x, worldPos.y);
        this.offsetX += mousePos.x - newScreenPos.x;
        this.offsetY -= mousePos.y - newScreenPos.y; // 修复Y方向滚轮缩放偏移
        
        this.drawNetwork();
    }

    // 元素检测和选择
    getIntersectionAt(mouseX, mouseY) {
        // 点击检测半径跟随缩放调整
        const scaleRatio = this.scale / this.baseScale;
        const radius = Math.max(8, Math.min(15, 10 * Math.sqrt(scaleRatio)));
        
        for (const intersection of this.intersections) {
            const screenPos = this.worldToScreen(intersection.x, intersection.y);
            const distance = Math.sqrt(
                (mouseX - screenPos.x) ** 2 + (mouseY - screenPos.y) ** 2
            );
            
            if (distance <= radius) {
                return intersection;
            }
        }
        
        return null;
    }

    getEdgeAt(mouseX, mouseY) {
        // 点击检测阈值跟随缩放调整
        const scaleRatio = this.scale / this.baseScale;
        const threshold = Math.max(8, Math.min(15, 10 * Math.sqrt(scaleRatio)));
        
        for (const edge of this.edges) {
            for (let i = 0; i < edge.points.length - 1; i++) {
                const p1 = this.worldToScreen(edge.points[i].x, edge.points[i].y);
                const p2 = this.worldToScreen(edge.points[i + 1].x, edge.points[i + 1].y);
                
                const distance = this.distanceToLineSegment(mouseX, mouseY, p1.x, p1.y, p2.x, p2.y);
                
                if (distance <= threshold) {
                    return edge;
                }
            }
        }
        
        return null;
    }

    distanceToLineSegment(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) {
            return Math.sqrt(A * A + B * B);
        }
        
        let param = dot / lenSq;
        param = Math.max(0, Math.min(1, param));
        
        const xx = x1 + param * C;
        const yy = y1 + param * D;
        
        const dx = px - xx;
        const dy = py - yy;
        
        return Math.sqrt(dx * dx + dy * dy);
    }

    toggleIntersectionSelection(intersectionId) {
        const intersection = this.intersections.find(i => i.id === intersectionId);
        if (!intersection) return;
        
        if (this.selectedIntersectionIds.has(intersectionId)) {
            this.selectedIntersectionIds.delete(intersectionId);
            intersection.selected = false;
        } else {
            this.selectedIntersectionIds.add(intersectionId);
            intersection.selected = true;
        }
        
        this.drawNetwork();
        this.updateSelectorDisplay();
    }

    toggleEdgeSelection(edgeId) {
        const edge = this.edges.find(e => e.id === edgeId);
        if (!edge) return;
        
        if (this.selectedEdgeIds.has(edgeId)) {
            this.selectedEdgeIds.delete(edgeId);
            edge.selected = false;
        } else {
            this.selectedEdgeIds.add(edgeId);
            edge.selected = true;
        }
        
        this.drawNetwork();
        this.updateSelectorDisplay();
    }

    // 绘制方法
    drawNetwork() {
        if (!this.ctx) return;
        
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 设置背景 - 使用与其他模块一致的背景色
        this.ctx.fillStyle = '#05202e';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制路段
        this.drawEdges();
        
        // 绘制交叉口
        if (this.allowIntersectionSelection) {
            this.drawIntersections();
        }
    }

    drawEdges() {
        // 计算跟随缩放的线条宽度
        const scaleRatio = this.scale / this.baseScale;
        const baseLineWidth = Math.max(1, 2 * Math.sqrt(scaleRatio));
        const selectedLineWidth = Math.max(2, 4 * Math.sqrt(scaleRatio));
        
        this.edges.forEach(edge => {
            if (edge.points.length < 2) return;
            
            // 设置颜色和线条宽度
            // 允许根据 edge.color 自定义颜色，否则保持原逻辑
            let strokeClr = '#666666';
            let lineW = baseLineWidth;
            if (edge.selected) {
                strokeClr = '#ff4444';
                lineW   = selectedLineWidth;
            } else if (edge.color) {
                strokeClr = edge.color;
            }

            this.ctx.strokeStyle = strokeClr;
            this.ctx.lineWidth   = lineW;
            
            this.ctx.beginPath();
            const firstPoint = this.worldToScreen(edge.points[0].x, edge.points[0].y);
            this.ctx.moveTo(firstPoint.x, firstPoint.y);
            
            for (let i = 1; i < edge.points.length; i++) {
                const point = this.worldToScreen(edge.points[i].x, edge.points[i].y);
                this.ctx.lineTo(point.x, point.y);
            }
            
            this.ctx.stroke();
            
            // 绘制路段名称或ID
            this.drawEdgeLabel(edge);
        });
    }

    drawEdgeLabel(edge) {
        if (edge.points.length < 2) return;
        
        // 计算路段中点位置用于显示标签
        const midIndex = Math.floor(edge.points.length / 2);
        const midPoint = this.worldToScreen(edge.points[midIndex].x, edge.points[midIndex].y);
        
        // 确定显示的文本：优先显示name，没有name则显示id
        const labelText = edge.name || edge.id;
        
        // 根据用户设置决定是否显示标签
        if (!this.showRoadNames) return;
        
        // 计算跟随缩放的字体大小，设置合理的范围
        const scaleRatio = this.scale / this.baseScale;
        const baseFontSize = 10;
        const fontSize = Math.max(8, Math.min(16, baseFontSize * Math.sqrt(scaleRatio)));
        
        this.ctx.save();
        this.ctx.fillStyle = edge.selected ? '#ff4444' : '#cccccc';
        this.ctx.font = `${fontSize}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 添加文字阴影效果，提高可读性，阴影大小也跟随缩放
        const shadowBlur = Math.max(1, fontSize * 0.2);
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
        this.ctx.shadowBlur = shadowBlur;
        this.ctx.shadowOffsetX = 1;
        this.ctx.shadowOffsetY = 1;
        
        this.ctx.fillText(labelText, midPoint.x, midPoint.y);
        this.ctx.restore();
    }

    drawIntersections() {
        this.intersections.forEach(intersection => {
            const screenPos = this.worldToScreen(intersection.x, intersection.y);
            
            // 计算跟随缩放的圆圈和字体大小
            const scaleRatio = this.scale / this.baseScale;
            const circleRadius = Math.max(4, Math.min(12, 6 * Math.sqrt(scaleRatio)));
            const fontSize = Math.max(8, Math.min(16, 10 * Math.sqrt(scaleRatio)));
            
            // 绘制圆圈
            this.ctx.beginPath();
            this.ctx.arc(screenPos.x, screenPos.y, circleRadius, 0, 2 * Math.PI);
            
            // 允许根据 intersection.color 自定义颜色，否则保持原逻辑
            let fillClr = '#4444ff';
            if (intersection.selected) {
                fillClr = '#ff4444';
            } else if (intersection.color) {
                fillClr = intersection.color;
            }

            this.ctx.fillStyle = fillClr;
            
            this.ctx.fill();
            this.ctx.strokeStyle = '#ffffff';
            this.ctx.lineWidth = Math.max(1, 2 * Math.sqrt(scaleRatio));
            this.ctx.stroke();
            
            // 根据用户设置决定是否显示标签
            if (this.showRoadNames) {
                // 绘制交叉口ID，位置跟随圆圈大小调整
                this.ctx.save();
                this.ctx.fillStyle = '#ffffff';
                this.ctx.font = `${fontSize}px Arial`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                
                // 添加文字阴影效果
                const shadowBlur = Math.max(1, fontSize * 0.2);
                this.ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
                this.ctx.shadowBlur = shadowBlur;
                this.ctx.shadowOffsetX = 1;
                this.ctx.shadowOffsetY = 1;
                
                this.ctx.fillText(intersection.id, screenPos.x, screenPos.y + circleRadius + fontSize + 5);
                this.ctx.restore();
            }
        });
    }

    /**
     * 检查是否有活跃的仿真正在运行
     */
    isSimulationActive() {
        // 检查全局仿真状态变量
        return (window.isReplaying || window.replayData ||
                (typeof isReplaying !== 'undefined' && isReplaying) ||
                (typeof replayData !== 'undefined' && replayData));
    }

    /**
     * 在固定容器中显示普通地图
     * @param {string} netFilePath 路网文件路径
     * @param {Object} options 可选配置，如高亮显示的路段等
     */
    async showNormalMap(netFilePath, options = {}) {
        try {
            // 如果有活跃的仿真，不显示普通地图
            if (this.isSimulationActive()) {
                console.log('仿真正在运行，跳过普通地图显示');
                return false;
            }

            // 解析路网文件
            await this.parseNetworkFile(netFilePath);

            // 获取固定容器
            const fixedContainer = document.getElementById('fixed-selector-container');
            if (!fixedContainer) {
                console.error('未找到固定容器');
                return;
            }

            // 隐藏默认占位图，准备显示地图
            const defaultMapView = document.getElementById('default-map-view');
            if (defaultMapView) {
                defaultMapView.style.display = 'none';
            }
            
            // 准备地图容器
            const mapContainer = document.getElementById('map-view');
            if (!mapContainer) {
                const newMapContainer = document.createElement('div');
                newMapContainer.id = 'map-view';
                newMapContainer.className = 'map-view';
                newMapContainer.style.width = '100%';
                newMapContainer.style.height = '100%';
                fixedContainer.appendChild(newMapContainer);
                
                // 创建画布
                const canvas = document.createElement('canvas');
                canvas.id = 'map-canvas';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                newMapContainer.appendChild(canvas);
                
                // 调整画布尺寸
                canvas.width = newMapContainer.clientWidth;
                canvas.height = newMapContainer.clientHeight;
                
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
            } else {
                // 使用现有的地图容器
                mapContainer.style.display = 'block';
                this.canvas = document.getElementById('map-canvas');
                this.ctx = this.canvas.getContext('2d');
            }
            
            // 重置交互相关属性，确保仅展示不允许选择
            this.allowEdgeSelection = false;
            this.allowIntersectionSelection = false;
            
            // 清空选择与颜色
            this.selectedEdgeIds.clear();
            this.selectedIntersectionIds.clear();
            this.edges.forEach(e => { 
                e.selected = false; 
                e.color = null; 
            });
            this.intersections.forEach(i => { 
                i.selected = false; 
                i.color = null; 
            });
            
            // 应用高亮选项
            if (options.highlightEdges && Array.isArray(options.highlightEdges)) {
                options.highlightEdges.forEach(item => {
                    const edge = this.edges.find(e => e.id === item.id);
                    if (edge) {
                        edge.color = item.color || '#00AAFF';
                    }
                });
            }
            
            if (options.highlightIntersections && Array.isArray(options.highlightIntersections)) {
                options.highlightIntersections.forEach(item => {
                    const intersection = this.intersections.find(i => i.id === item.id);
                    if (intersection) {
                        intersection.color = item.color || '#FFAA00';
                    }
                });
            }
            
            // 自适应视图并绘制
            this.baseScale = 1;
            this.fitToCanvas();
            this.drawNetwork();
            
            return true;
        } catch (error) {
            console.error('显示普通地图失败:', error);
            return false;
        }
    }
    
    /**
     * 隐藏地图，显示默认视图
     */
    hideMap() {
        // 隐藏地图容器
        const mapContainer = document.getElementById('map-view');
        if (mapContainer) {
            mapContainer.style.display = 'none';
        }
        
        // 显示默认地图视图
        const defaultMapView = document.getElementById('default-map-view');
        if (defaultMapView) {
            defaultMapView.style.display = 'flex';
        }
        
        this.canvas = null;
        this.ctx = null;
    }

    /**
     * 重置选择器状态
     */
    reset() {
        // 清空选择状态
        this.selectedEdgeIds.clear();
        this.selectedIntersectionIds.clear();
        
        // 重置边和交叉口状态
        if (this.edges) {
            this.edges.forEach(edge => {
                edge.selected = false;
                edge.color = null;
            });
        }
        
        if (this.intersections) {
            this.intersections.forEach(intersection => {
                intersection.selected = false;
                intersection.color = null;
            });
        }
        
        // 重置画布相关属性
        this.canvas = null;
        this.ctx = null;
        this.allowEdgeSelection = true;
        this.allowIntersectionSelection = true;
        
        console.log('网络选择器状态已重置');
    }
}

// 创建全局选择器实例
window.networkSelector = new FrontendNetworkSelector(); 
