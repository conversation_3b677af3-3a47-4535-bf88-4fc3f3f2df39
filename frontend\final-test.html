<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试 - vw/vh 方案验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #05202e;
            color: #95D7E3;
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .test-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .test-info {
            background: rgba(48, 171, 232, 0.1);
            border: 0.2vw solid #30abe8;
            border-radius: 1vw;
            padding: 2vh 3vw;
            text-align: center;
            max-width: 60vw;
        }

        .test-info h1 {
            font-size: 2vw;
            color: #30abe8;
            margin-bottom: 1vh;
        }

        .test-info p {
            font-size: 1vw;
            margin-bottom: 1vh;
            line-height: 1.5;
        }

        .zoom-display {
            background: rgba(255, 198, 25, 0.2);
            border: 0.1vw solid #ffc619;
            border-radius: 0.5vw;
            padding: 1vh 2vw;
            margin: 1vh 0;
            font-size: 1.2vw;
            color: #ffc619;
            font-weight: bold;
        }

        .test-buttons {
            display: flex;
            gap: 1vw;
            margin-top: 2vh;
        }

        .test-btn {
            background: transparent;
            color: #30abe8;
            border: 0.1vw solid #30abe8;
            border-radius: 0.5vw;
            padding: 1vh 2vw;
            font-size: 0.9vw;
            cursor: pointer;
            transition: all 0.3s;
        }

        .test-btn:hover {
            background: rgba(48, 171, 232, 0.2);
        }

        .test-btn.primary {
            background: #30abe8;
            color: white;
        }

        .test-btn.primary:hover {
            background: #2a7dc9;
        }

        .instructions {
            margin-top: 2vh;
            font-size: 0.8vw;
            color: #aaaaaa;
            text-align: left;
        }

        .instructions ul {
            list-style: none;
            padding-left: 1vw;
        }

        .instructions li {
            margin-bottom: 0.5vh;
        }

        .instructions li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
        }

        /* 隐藏的主页面 */
        .main-page {
            opacity: 0.3;
            pointer-events: none;
        }

        /* 响应式调整 */
        @media (max-aspect-ratio: 4/3) {
            .test-info h1 { font-size: 3vw; }
            .test-info p { font-size: 1.5vw; }
            .zoom-display { font-size: 1.8vw; }
            .test-btn { font-size: 1.3vw; }
            .instructions { font-size: 1.2vw; }
        }

        @media (min-aspect-ratio: 21/9) {
            .test-info h1 { font-size: 1.5vw; }
            .test-info p { font-size: 0.8vw; }
            .zoom-display { font-size: 1vw; }
            .test-btn { font-size: 0.7vw; }
            .instructions { font-size: 0.6vw; }
        }
    </style>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="network_selector.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
</head>
<body>
    <!-- 测试覆盖层 -->
    <div class="test-overlay" id="testOverlay">
        <div class="test-info">
            <h1>🎯 vw/vh 视口单位方案 - 最终验证</h1>
            <p>您的页面已成功转换为使用 vw/vh 视口单位的响应式方案</p>
            
            <div class="zoom-display" id="zoomDisplay">
                当前缩放: 100% | 视口: 1920×1080
            </div>

            <div class="instructions">
                <p><strong>测试说明：</strong></p>
                <ul>
                    <li>点击"开始测试"进入主页面</li>
                    <li>使用 Ctrl + 滚轮 或 Ctrl + +/- 缩放页面</li>
                    <li>观察所有元素是否保持完美的比例关系</li>
                    <li>在 50%-200% 缩放范围内测试</li>
                    <li>调整浏览器窗口大小验证响应性</li>
                </ul>
            </div>

            <div class="test-buttons">
                <button class="test-btn primary" onclick="startTest()">开始测试</button>
                <button class="test-btn" onclick="showComparison()">查看对比</button>
                <button class="test-btn" onclick="showGuide()">查看文档</button>
            </div>
        </div>
    </div>

    <!-- 主页面内容 -->
    <div class="main-page" id="mainPage">
        <div class="container">
            <!-- 修改为水平布局 -->
            <main>
                <div class="left-panel">
                    <!-- 配置部分的内容 -->
                    <section class="card" id="networkSection">
                        <h2>大型活动场景</h2>
                        <div class="option-group">
                            <div class="option">
                                <input type="radio" id="net-preset" name="network" value="preset" checked>
                                <label for="net-preset">雄安体育场大型体育赛事</label>
                            </div>
                            <div class="option">
                                <input type="radio" id="net-custom" name="network" value="custom">
                                <label for="net-custom">自定义场景</label>
                            </div>
                            <button class="upload-btn" id="uploadNetBtn">
                                <i class="bi bi-upload"></i> 加载文件(*.net.xml)
                            </button>
                        </div>
                    </section>

                    <section class="card" id="trafficSection">
                        <h2>交通需求</h2>
                        <div class="option-group">
                            <div class="option">
                                <input type="radio" id="traffic-preset" name="traffic" value="preset" checked>
                                <label for="traffic-preset">预设</label>
                            </div>
                            <div class="option">
                                <input type="radio" id="traffic-custom" name="traffic" value="custom">
                                <label for="traffic-custom">自定义</label>
                            </div>
                            <button class="upload-btn" id="uploadRouBtn">
                                <i class="bi bi-upload"></i> 加载文件(*.rou.xml)
                            </button>
                        </div>
                    </section>

                    <div class="action-buttons">
                        <button class="run-btn">运行方案</button>
                        <button class="load-btn" id="loadResultBtn">
                            <i class="bi bi-file-earmark-text"></i> 加载已有结果
                        </button>
                        <button class="history-btn" id="historyBtn">
                            <i class="bi bi-clock-history"></i> 历史方案
                        </button>
                    </div>
                </div>

                <!-- 右侧主内容区 -->
                <div class="right-panel">
                    <!-- 左侧区域：地图和仿真概况 -->
                    <div class="map-section">
                        <!-- 固定的选择器/地图容器 -->
                        <div id="fixed-selector-container" class="fixed-selector-container map-container">
                            <div id="default-map-view" class="default-map-view">
                                <div class="map-placeholder">
                                    <i class="bi bi-map"></i>
                                    <p>路网地图</p>
                                    <p style="font-size: 0.6vw; margin-top: 0.5vh; color: #ffc619;">
                                        ✅ 使用 vw/vh 单位 - 完美缩放一致性
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 仿真概况与配置 -->
                        <section class="result-card simulation-info-config" id="simulationInfoConfig">
                            <div class="result-header">
                                <h2 class="simulation-title">仿真概况与配置</h2>
                            </div>
                            <div class="simulation-info">
                                <div>
                                    <span><strong>仿真ID:</strong> <span id="simId">250702174456</span></span>
                                    <span class="sim-time"><strong>开始时间:</strong> <span id="startTime">2025-07-02 17:44:56</span></span>
                                </div>
                            </div>
                            <div class="config-summary">
                                <div class="config-comparison">
                                    <div class="current-config">
                                        <h3>当前方案</h3>
                                        <div class="config-item" id="networkConfig">
                                            <span><strong>路网配置:</strong> <span>预设路网 - 使用 vw/vh 单位</span></span>
                                        </div>
                                        <div class="config-item" id="signalConfig">
                                            <span><strong>信号配置:</strong> <span>响应式设计 - 完美缩放</span></span>
                                        </div>
                                        <div class="config-item" id="trafficConfig">
                                            <span><strong>交通需求:</strong> <span>视口单位 - 一致性保证</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                    
                    <!-- 右侧区域：指标图表 -->
                    <div class="charts-section">
                        <div class="charts-header">
                            <h3>交通指标分析</h3>
                        </div>
                        <div class="charts-container">
                            <section class="result-card">
                                <div class="result-header">
                                    <h2 class="chart-title">平均行程时间对比</h2>
                                </div>
                                <div class="chart-container">
                                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.5); font-size: 0.7vw;">
                                        图表区域 - vw/vh 单位
                                    </div>
                                </div>
                            </section>

                            <section class="result-card">
                                <div class="result-header">
                                    <h2 class="chart-title">平均等待时间对比</h2>
                                </div>
                                <div class="chart-container">
                                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.5); font-size: 0.7vw;">
                                        图表区域 - 响应式缩放
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        function updateZoomDisplay() {
            const zoomDisplay = document.getElementById('zoomDisplay');
            const zoom = Math.round((window.outerWidth / window.innerWidth) * 100);
            const vw = window.innerWidth;
            const vh = window.innerHeight;
            zoomDisplay.textContent = `当前缩放: ${zoom}% | 视口: ${vw}×${vh}`;
        }

        function startTest() {
            document.getElementById('testOverlay').style.display = 'none';
            document.getElementById('mainPage').style.opacity = '1';
            document.getElementById('mainPage').style.pointerEvents = 'auto';
        }

        function showComparison() {
            window.open('comparison-test.html', '_blank');
        }

        function showGuide() {
            window.open('viewport-units-guide.md', '_blank');
        }

        // 初始化
        updateZoomDisplay();

        // 监听窗口变化
        window.addEventListener('resize', updateZoomDisplay);

        console.log('🎯 vw/vh 视口单位方案已成功应用！');
        console.log('📋 测试重点：');
        console.log('1. 使用 Ctrl + 滚轮缩放页面');
        console.log('2. 观察所有元素是否保持完美比例');
        console.log('3. 在不同缩放比例下测试交互功能');
        console.log('✅ 预期效果：完美的缩放一致性');
    </script>
</body>
</html>
