# 地图重复刷新问题修复说明

## 问题描述

在每次关闭选择器显示后，地图会出现重复刷新的问题：
1. 第一次刷新：显示带卫星图的地图
2. 第二次刷新：再次刷新为路网骨架

这导致了不必要的重复加载和用户体验不佳。

## 问题原因分析

### 双重刷新机制

1. **`networkSelector.hideSelector()`** - 在选择器关闭时自动恢复默认地图视图
2. **选择器回调函数中的 `initializeDefaultMap()`** - 在回调函数中再次调用地图初始化

### 具体代码位置

在 `frontend/script.js` 中，所有选择器的回调函数都有类似的代码：

```javascript
// 恢复地图视图
embeddedView.style.display = 'none';
// 延迟恢复普通地图视图，避免与仿真冲突
setTimeout(() => {
    initializeDefaultMap(); // 这里导致了重复刷新
}, 100);
```

同时在 `frontend/network_selector.js` 的 `hideSelector()` 函数中：

```javascript
// 恢复默认地图视图
const defaultMapView = document.getElementById('default-map-view');
if (defaultMapView) {
    defaultMapView.style.display = 'flex'; // 这里已经恢复了视图
}
```

## 修复方案

### 1. 移除回调函数中的重复调用

修改了以下选择器回调函数中的代码：

#### 分析路段选择器回调
```javascript
// 修改前
setTimeout(() => {
    initializeDefaultMap();
}, 100);

// 修改后
// 注意：不需要调用 initializeDefaultMap()，因为 networkSelector.hideSelector() 已经会恢复默认视图
```

#### 出入口路段选择器回调
```javascript
// 修改前
setTimeout(() => {
    initializeDefaultMap();
}, 100);

// 修改后
// 注意：不需要调用 initializeDefaultMap()，因为 networkSelector.hideSelector() 已经会恢复默认视图
```

#### 限行路段选择器回调
```javascript
// 修改前
setTimeout(() => {
    initializeDefaultMap();
}, 100);

// 修改后
// 注意：不需要调用 initializeDefaultMap()，因为 networkSelector.hideSelector() 已经会恢复默认视图
```

#### 信控优化选择器回调
```javascript
// 修改前
setTimeout(() => {
    initializeDefaultMap();
}, 100);

// 修改后
// 注意：不需要调用 initializeDefaultMap()，因为 networkSelector.hideSelector() 已经会恢复默认视图
```

### 2. 增强 hideSelector() 函数

在 `frontend/network_selector.js` 中增强了 `hideSelector()` 函数：

```javascript
hideSelector() {
    // 如果是嵌入模式，不移除DOM，而是恢复默认地图视图
    if (this.isEmbedded && this.embedParent) {
        // 隐藏选择器视图
        this.embedParent.style.display = 'none';
        
        // 恢复默认地图视图
        const defaultMapView = document.getElementById('default-map-view');
        if (defaultMapView) {
            defaultMapView.style.display = 'flex';
            
            // 检查是否需要重新显示地图
            const mapPlaceholder = defaultMapView.querySelector('.map-placeholder');
            if (mapPlaceholder && this.edges && this.edges.length > 0) {
                // 如果有路网数据，尝试显示普通地图
                setTimeout(() => {
                    this.showNormalMapInDefaultView();
                }, 100);
            }
        }
    }
    // ... 其他代码
}
```

### 3. 添加智能地图显示函数

添加了 `showNormalMapInDefaultView()` 函数来智能处理地图显示：

```javascript
showNormalMapInDefaultView() {
    try {
        // 如果有活跃的仿真，不显示普通地图
        if (this.isSimulationActive()) {
            console.log('仿真正在运行，跳过普通地图显示');
            return;
        }

        // 获取默认地图视图容器
        const defaultMapView = document.getElementById('default-map-view');
        if (!defaultMapView) return;

        // 检查是否已经有地图内容
        const existingCanvas = defaultMapView.querySelector('canvas');
        if (existingCanvas) {
            console.log('地图已存在，跳过重复加载');
            return;
        }

        // 获取当前路网文件路径并显示地图
        const getCurrentNetFilePath = window.getCurrentNetFilePath;
        if (typeof getCurrentNetFilePath === 'function') {
            const netFilePath = getCurrentNetFilePath();
            if (netFilePath) {
                this.showNormalMap(netFilePath);
            }
        }
    } catch (error) {
        console.error('在默认视图中显示地图失败:', error);
    }
}
```

### 4. 暴露必要的全局函数

将 `getCurrentNetFilePath` 函数暴露到全局作用域：

```javascript
// 将函数暴露到全局作用域
window.getCurrentNetFilePath = getCurrentNetFilePath;
```

## 修复效果

### 修复前的问题
1. 选择器关闭后地图会刷新两次
2. 第一次显示卫星图，第二次显示路网骨架
3. 用户体验不佳，加载时间长

### 修复后的效果
1. 选择器关闭后地图只刷新一次
2. 直接显示正确的路网地图
3. 避免了不必要的重复加载
4. 提升了用户体验

## 涉及的文件

### 主要修改文件
1. **frontend/script.js**
   - 移除了所有选择器回调函数中的重复 `initializeDefaultMap()` 调用
   - 暴露了 `getCurrentNetFilePath` 函数到全局作用域

2. **frontend/network_selector.js**
   - 增强了 `hideSelector()` 函数
   - 添加了 `showNormalMapInDefaultView()` 智能地图显示函数

## 测试验证

### 测试步骤
1. 打开 `http://localhost:8888/frontend/index.html`
2. 点击任意选择器按钮（如"选择开放的出入口"）
3. 在选择器中选择一些元素
4. 点击"确认选择"关闭选择器
5. 观察地图是否只刷新一次

### 预期结果
- 选择器关闭后，地图应该只刷新一次
- 直接显示正确的路网地图，不会出现先显示卫星图再显示路网的情况
- 加载速度更快，用户体验更好

## 注意事项

1. **兼容性**：修改保持了与现有仿真功能的兼容性
2. **错误处理**：添加了适当的错误处理和状态检查
3. **性能优化**：避免了重复的网络请求和DOM操作
4. **代码维护**：代码结构更清晰，注释更详细

## 后续优化建议

1. 可以考虑添加地图缓存机制，进一步提升性能
2. 可以添加加载状态指示器，提升用户体验
3. 可以考虑预加载地图数据，减少首次加载时间
