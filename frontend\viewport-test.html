<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大型活动交通组织管理系统 - vw/vh测试版</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        /* 测试页面特有样式 */
        .zoom-info {
            position: fixed;
            top: 1vh;
            right: 1vw;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5vh 1vw;
            border-radius: 0.3vw;
            font-size: 0.7vw;
            z-index: 1000;
            border: 0.05vw solid #30abe8;
        }

        /* 确保使用视口单位的样式覆盖 */
        body {
            background-color: #05202e;
            color: #95D7E3;
            line-height: 1.4;
            width: 100vw;
            height: 100vh;
            overflow-x: hidden;
        }

        /* 页面布局调整 - 使用视口单位 */
        .container {
            position: relative;
            height: 100vh;
            width: 100vw;
        }

        /* 主容器设置 */
        main {
            position: relative;
            padding-top: 0;
            width: 100vw;
            height: 100vh;
        }

        /* 左侧栏固定定位，使用视口单位 */
        main > div:first-child {
            position: fixed;
            top: 5vh;
            left: 0.7vw;
            width: 20vw;
            height: 94vh;
            overflow-y: auto;
            z-index: 10;
        }

        /* 右侧栏固定定位，使用视口单位 */
        main > div:nth-child(2) {
            position: fixed;
            top: 4vh;
            left: 21.5vw;
            right: 0.7vw;
            height: 95vh;
            overflow-y: auto;
            z-index: 10;
        }


    </style>
</head>
<body>
    <div class="zoom-info" id="zoomInfo">
        缩放比例: <span id="zoomLevel">100%</span> | 视口: <span id="viewport"></span>
    </div>

    <div class="container">
        <!-- 修改为水平布局 -->
        <main style="display: flex; flex-direction: row; gap: 0.7vw;">
            <div style="flex: 1; max-width: 20vw; margin-top: 4vh;">
                <!-- 配置部分的内容 -->
                <section class="card" id="networkSection">
                    <h2>大型活动场景</h2>
                    <div class="option-group">
                        <div class="option">
                            <input type="radio" id="net-preset" name="network" value="preset" checked>
                            <label for="net-preset">雄安体育场大型体育赛事</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="net-custom" name="network" value="custom">
                            <label for="net-custom">自定义场景</label>
                        </div>
                        <button class="upload-btn" id="uploadNetBtn">
                            <i class="bi bi-upload"></i> 加载文件(*.net.xml)
                        </button>
                    </div>
                </section>

                <section class="card" id="trafficSection">
                    <h2>交通需求</h2>
                    <div class="option-group">
                        <div class="option">
                            <input type="radio" id="traffic-preset" name="traffic" value="preset" checked>
                            <label for="traffic-preset">预设</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="traffic-custom" name="traffic" value="custom">
                            <label for="traffic-custom">自定义</label>
                        </div>
                        <button class="upload-btn" id="uploadRouBtn">
                            <i class="bi bi-upload"></i> 加载文件(*.rou.xml)
                        </button>
                    </div>
                    <div class="sub-options">
                        <div class="vehicle-time-container">
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>车辆类型：</label>
                                <div class="select-wrapper">
                                    <select id="vehicleType">
                                        <option value="general">一般车辆</option>
                                        <option value="vip">贵宾专车</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="vehicle-time-container">
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>交通规模：</label>
                                <div class="select-wrapper">
                                    <select id="trafficScale">
                                        <option value="small">小</option>
                                        <option value="medium" selected>中</option>
                                        <option value="large">大</option>
                                    </select>
                                </div>
                            </div>
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>组织时段：</label>
                                <div class="select-wrapper">
                                    <select id="timePhase">
                                        <option value="entrance">进场</option>
                                        <option value="exit">离场</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="card" id="organizationSection">
                    <h2>组织方案</h2>
                    <div class="sub-options">
                        <div class="org-options-layout">
                            <!-- 第一列：道路相关选项 -->
                            <div class="org-column">
                                <div class="org-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>出入口方案：</label>
                                        <div class="select-wrapper">
                                            <button class="select-btn" id="selectEntranceBtn">
                                                <i class="bi bi-geo-alt"></i> 选择开放的出入口
                                            </button>
                                        </div>
                                    </div>
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>道路限行：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="roadRestriction">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二列：车辆和信号相关选项 -->
                            <div class="org-column">
                                <div class="org-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>贵宾专车优先通行：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="vipPriority">
                                        </div>
                                    </div>
                                </div>

                                <div class="org-group signal-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>信号配时：</label>
                                        <div class="signal-options">
                                            <div class="option-group compact">
                                                <div class="option">
                                                    <input type="radio" id="signal-preset" name="signal" value="preset" checked>
                                                    <label for="signal-preset">预设</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" id="signal-custom" name="signal" value="custom">
                                                    <label for="signal-custom">自定义</label>
                                                </div>
                                            </div>
                                            <button class="upload-btn" id="uploadAddBtn">
                                                <i class="bi bi-upload"></i> 加载文件(*.add.xml)
                                            </button>
                                        </div>
                                    </div>
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>信控优化：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="signalOptimization">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="action-buttons">
                    <button class="run-btn">运行方案</button>
                    <button class="load-btn" id="loadResultBtn">
                        <i class="bi bi-file-earmark-text"></i> 加载已有结果
                    </button>
                    <button class="history-btn" id="historyBtn">
                        <i class="bi bi-clock-history"></i> 历史方案
                    </button>
                </div>
            </div>

            <!-- 右侧主内容区 - 左右两栏布局：左侧为地图和仿真概况，右侧为指标图表 -->
            <div style="flex: 3; display: flex; flex-direction: row; margin-top: 2.7vh;">
                <!-- 左侧区域：地图和仿真概况（垂直排列） -->
                <div style="flex: 7; display: flex; flex-direction: column; margin-right: 1vw;">
                    <!-- 固定的选择器/地图容器 -->
                    <div id="fixed-selector-container" class="fixed-selector-container" style="margin-bottom: 1vh;">
                        <!-- 默认显示地图 -->
                        <div id="default-map-view" class="default-map-view">
                            <div class="map-placeholder">
                                <i class="bi bi-map"></i>
                                <p>路网地图</p>
                                <p style="font-size: 0.6vw; margin-top: 0.5vh; color: #ffc619;">
                                    🎯 测试缩放效果：使用 Ctrl + 滚轮 或 Ctrl + +/- 缩放页面
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 仿真概况与配置 -->
                    <section class="result-card" id="simulationInfoConfig" style="flex-grow: 1; overflow-y: auto; height: 18.5vh; margin-bottom: 0;">
                        <div class="result-header" style="padding-bottom: 0.2vh; margin-bottom: 0.2vh;">
                            <h2 style="font-size: 0.9vw; color: #8acdff;">仿真概况与配置</h2>
                        </div>
                        <div class="simulation-info" style="margin-bottom:0.3vh !important;">
                            <div>
                                <span><strong>仿真ID:</strong> <span id="simId">250702174456</span></span>
                                <span style="margin-left:1vw"><strong>开始时间:</strong> <span id="startTime">2025-07-02 17:44:56</span></span>
                            </div>
                        </div>
                        <div class="config-summary" style="font-size: 0.7vw;">
                            <div class="config-comparison">
                                <div class="current-config" style="font-size:0.7vw;">
                                    <h3 style="font-size:0.85vw; margin: 0.3vh 0;">当前方案</h3>
                                    <div class="config-item" id="networkConfig">
                                        <span><strong>路网配置:</strong> <span>预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)</span></span>
                                    </div>
                                    <div class="config-item" id="signalConfig">
                                        <span><strong>信号配置:</strong> <span>预设配时 + 自定义优化(2个交叉口)</span></span>
                                    </div>
                                    <div class="config-item" id="trafficConfig">
                                        <span><strong>交通需求:</strong> <span>预设需求 - 进场场景 + 中规模 + 贵宾专车</span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- 右侧区域：指标图表 -->
                <div style="flex: 3; display: flex; flex-direction: column;">
                    <div style="display: flex; align-items: center; margin-bottom: 0.7vh; padding-left: 0.3vw;">
                        <h3 style="margin: 0; font-size: 1.1vw; color: #ffffff;">交通指标分析</h3>
                    </div>
                    <div style="display:flex; flex-direction: column; gap:0.7vh; max-height: 85vh; overflow-y: auto; padding: 0 0.7vw 0 0.3vw;">
                        <section class="result-card">
                            <div class="result-header" style="padding-bottom: 0.2vh; margin-bottom: 0.2vh;">
                                <h2 style="font-size: 0.9vw; color: #8acdff;">平均行程时间对比</h2>
                            </div>
                            <div class="chart-container" style="height: 9.3vh;">
                                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.5); font-size: 0.7vw;">
                                    图表区域 - 使用 vw/vh 单位
                                </div>
                            </div>
                        </section>

                        <section class="result-card">
                            <div class="result-header" style="padding-bottom: 0.2vh; margin-bottom: 0.2vh;">
                                <h2 style="font-size: 0.9vw; color: #8acdff;">平均等待时间对比</h2>
                            </div>
                            <div class="chart-container" style="height: 9.3vh;">
                                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.5); font-size: 0.7vw;">
                                    图表区域 - 响应式缩放
                                </div>
                            </div>
                        </section>

                        <section class="result-card">
                            <div class="result-header" style="padding-bottom: 0.2vh; margin-bottom: 0.2vh;">
                                <h2 style="font-size: 0.9vw; color: #8acdff;">平均等待次数对比</h2>
                            </div>
                            <div class="chart-container" style="height: 9.3vh;">
                                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.5); font-size: 0.7vw;">
                                    图表区域 - 视口单位
                                </div>
                            </div>
                        </section>

                        <section class="result-card">
                            <div class="result-header" style="padding-bottom: 0.2vh; margin-bottom: 0.2vh;">
                                <h2 style="font-size: 0.9vw; color: #8acdff;">时间损失与延误对比</h2>
                            </div>
                            <div class="chart-container" style="height: 9.3vh;">
                                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: rgba(255,255,255,0.5); font-size: 0.7vw;">
                                    图表区域 - 完美缩放
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function updateZoomInfo() {
            const zoomLevel = document.getElementById('zoomLevel');
            const viewport = document.getElementById('viewport');

            // 计算缩放比例
            const zoom = Math.round((window.outerWidth / window.innerWidth) * 100);
            const vw = window.innerWidth;
            const vh = window.innerHeight;

            zoomLevel.textContent = zoom + '%';
            viewport.textContent = vw + 'x' + vh;
        }

        // 初始化
        updateZoomInfo();

        // 监听窗口变化
        window.addEventListener('resize', updateZoomInfo);

        // 监听缩放变化
        let resizeTimer;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(updateZoomInfo, 100);
        });

        // 添加测试说明
        console.log('🎯 视口单位 (vw/vh) 测试页面已加载');
        console.log('📋 测试步骤：');
        console.log('1. 使用 Ctrl + 滚轮 或 Ctrl + +/- 来缩放页面');
        console.log('2. 调整浏览器窗口大小');
        console.log('3. 观察所有元素是否保持相同的视觉比例');
        console.log('4. 对比原页面 (index.html) 的缩放效果');
        console.log('✅ 预期效果：所有元素在任何缩放比例下都保持完美的比例关系');
    </script>
</body>
</html>
