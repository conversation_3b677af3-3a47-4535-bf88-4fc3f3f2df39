<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视口单位测试页面</title>
    <style>
        /* 使用视口单位的测试样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #05202e 0%, #102735 100%);
            color: #95D7E3;
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .test-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 2vh 2vw;
        }

        .header {
            height: 8vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(48, 171, 232, 0.1);
            border-radius: 0.5vw;
            margin-bottom: 2vh;
        }

        .header h1 {
            font-size: 2vw;
            color: #30abe8;
        }

        .main-content {
            flex: 1;
            display: flex;
            gap: 2vw;
        }

        .left-panel {
            width: 25vw;
            background: rgba(16, 39, 53, 0.8);
            border-radius: 0.5vw;
            padding: 1.5vh 1vw;
            display: flex;
            flex-direction: column;
            gap: 1vh;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1vh;
        }

        .map-area {
            height: 60vh;
            background: rgba(30, 30, 30, 0.8);
            border-radius: 0.5vw;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 0.1vw solid rgba(48, 171, 232, 0.3);
        }

        .map-placeholder {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
        }

        .map-placeholder i {
            font-size: 4vw;
            margin-bottom: 1vh;
            display: block;
        }

        .map-placeholder p {
            font-size: 1.2vw;
        }

        .charts-area {
            flex: 1;
            display: flex;
            gap: 1vw;
        }

        .chart-container {
            flex: 1;
            background: rgba(25, 35, 50, 0.8);
            border-radius: 0.5vw;
            padding: 1vh;
            display: flex;
            flex-direction: column;
        }

        .chart-title {
            font-size: 0.9vw;
            color: #8acdff;
            margin-bottom: 0.5vh;
            text-align: center;
        }

        .chart-content {
            flex: 1;
            background: rgba(5, 32, 46, 0.6);
            border-radius: 0.3vw;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.8vw;
        }

        .config-item {
            background: rgba(5, 32, 46, 0.6);
            border-radius: 0.3vw;
            padding: 1vh 0.8vw;
            border: 0.05vw solid rgba(48, 171, 232, 0.2);
        }

        .config-title {
            font-size: 0.9vw;
            color: #30abe8;
            margin-bottom: 0.5vh;
        }

        .config-option {
            display: flex;
            align-items: center;
            gap: 0.5vw;
            margin-bottom: 0.3vh;
            font-size: 0.7vw;
        }

        .radio-btn {
            width: 0.8vw;
            height: 0.8vw;
            border: 0.05vw solid #30abe8;
            border-radius: 50%;
            background: transparent;
        }

        .radio-btn.checked {
            background: #30abe8;
        }

        .run-button {
            background: transparent;
            color: #ffc619;
            border: 0.1vw solid #ffc619;
            border-radius: 0.3vw;
            padding: 1vh 2vw;
            font-size: 0.9vw;
            cursor: pointer;
            margin-top: 1vh;
            transition: all 0.3s;
        }

        .run-button:hover {
            background: rgba(255, 198, 25, 0.2);
            transform: translateY(-0.1vh);
        }

        .zoom-info {
            position: fixed;
            top: 1vh;
            right: 1vw;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5vh 1vw;
            border-radius: 0.3vw;
            font-size: 0.7vw;
            z-index: 1000;
        }

        /* 响应式调整 */
        @media (max-aspect-ratio: 4/3) {
            .header h1 { font-size: 3vw; }
            .map-placeholder i { font-size: 6vw; }
            .map-placeholder p { font-size: 1.8vw; }
        }

        @media (min-aspect-ratio: 21/9) {
            .header h1 { font-size: 1.5vw; }
            .map-placeholder i { font-size: 3vw; }
            .map-placeholder p { font-size: 0.9vw; }
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
</head>
<body>
    <div class="test-container">
        <div class="zoom-info" id="zoomInfo">
            缩放比例: 100% | 视口: <span id="viewport"></span>
        </div>

        <div class="header">
            <h1>视口单位 (vw/vh) 响应式测试</h1>
        </div>

        <div class="main-content">
            <div class="left-panel">
                <div class="config-item">
                    <div class="config-title">场景配置</div>
                    <div class="config-option">
                        <div class="radio-btn checked"></div>
                        <span>预设场景</span>
                    </div>
                    <div class="config-option">
                        <div class="radio-btn"></div>
                        <span>自定义场景</span>
                    </div>
                </div>

                <div class="config-item">
                    <div class="config-title">交通需求</div>
                    <div class="config-option">
                        <div class="radio-btn checked"></div>
                        <span>中等规模</span>
                    </div>
                    <div class="config-option">
                        <div class="radio-btn"></div>
                        <span>大规模</span>
                    </div>
                </div>

                <div class="config-item">
                    <div class="config-title">组织方案</div>
                    <div class="config-option">
                        <div class="radio-btn"></div>
                        <span>道路限行</span>
                    </div>
                    <div class="config-option">
                        <div class="radio-btn checked"></div>
                        <span>信号优化</span>
                    </div>
                </div>

                <button class="run-button">运行仿真</button>
            </div>

            <div class="right-panel">
                <div class="map-area">
                    <div class="map-placeholder">
                        <i class="bi bi-map"></i>
                        <p>路网地图区域</p>
                        <p style="font-size: 0.8vw; margin-top: 0.5vh;">此区域在不同缩放比例下保持相同的视觉比例</p>
                    </div>
                </div>

                <div class="charts-area">
                    <div class="chart-container">
                        <div class="chart-title">平均行程时间</div>
                        <div class="chart-content">图表区域 1</div>
                    </div>
                    <div class="chart-container">
                        <div class="chart-title">平均等待时间</div>
                        <div class="chart-content">图表区域 2</div>
                    </div>
                    <div class="chart-container">
                        <div class="chart-title">时间损失</div>
                        <div class="chart-content">图表区域 3</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateZoomInfo() {
            const zoomInfo = document.getElementById('zoomInfo');
            const viewport = document.getElementById('viewport');
            
            const zoom = Math.round(window.devicePixelRatio * 100);
            const vw = window.innerWidth;
            const vh = window.innerHeight;
            
            zoomInfo.querySelector('span').textContent = zoom + '%';
            viewport.textContent = vw + 'x' + vh;
        }

        // 初始化
        updateZoomInfo();

        // 监听窗口变化
        window.addEventListener('resize', updateZoomInfo);
        
        // 监听缩放变化
        let resizeTimer;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(updateZoomInfo, 100);
        });

        // 添加测试说明
        console.log('视口单位测试页面已加载');
        console.log('请尝试以下操作来测试响应性：');
        console.log('1. 使用 Ctrl + 滚轮 或 Ctrl + +/- 来缩放页面');
        console.log('2. 调整浏览器窗口大小');
        console.log('3. 观察所有元素是否保持相同的视觉比例');
    </script>
</body>
</html>
