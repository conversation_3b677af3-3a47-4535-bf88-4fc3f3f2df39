<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缩放效果对比测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #05202e;
            color: #95D7E3;
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            overflow-x: hidden;
        }

        .comparison-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            height: 8vh;
            background: rgba(48, 171, 232, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 0.1vw solid #30abe8;
        }

        .header h1 {
            font-size: 1.5vw;
            color: #30abe8;
            text-align: center;
        }

        .controls {
            height: 6vh;
            background: rgba(16, 39, 53, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2vw;
            border-bottom: 0.05vw solid rgba(255, 255, 255, 0.1);
        }

        .zoom-info {
            font-size: 0.8vw;
            color: #ffc619;
            background: rgba(0, 0, 0, 0.5);
            padding: 0.3vh 0.8vw;
            border-radius: 0.3vw;
            border: 0.05vw solid #ffc619;
        }

        .test-buttons {
            display: flex;
            gap: 1vw;
        }

        .test-btn {
            background: transparent;
            color: #30abe8;
            border: 0.1vw solid #30abe8;
            border-radius: 0.3vw;
            padding: 0.4vh 1vw;
            font-size: 0.7vw;
            cursor: pointer;
            transition: all 0.3s;
        }

        .test-btn:hover {
            background: rgba(48, 171, 232, 0.2);
        }

        .frames-container {
            flex: 1;
            display: flex;
            gap: 0.5vw;
            padding: 0.5vh 0.5vw;
        }

        .frame {
            flex: 1;
            border: 0.1vw solid rgba(255, 255, 255, 0.2);
            border-radius: 0.5vw;
            overflow: hidden;
            position: relative;
            background: rgba(0, 0, 0, 0.3);
        }

        .frame-header {
            background: rgba(16, 39, 53, 0.9);
            padding: 0.5vh 1vw;
            border-bottom: 0.05vw solid rgba(255, 255, 255, 0.1);
            font-size: 0.8vw;
            color: #ffffff;
            text-align: center;
        }

        .frame-content {
            height: calc(100% - 4vh);
            position: relative;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            transform-origin: top left;
        }

        .instructions {
            position: absolute;
            bottom: 1vh;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #ffc619;
            padding: 1vh 2vw;
            border-radius: 0.5vw;
            font-size: 0.7vw;
            text-align: center;
            border: 0.05vw solid #ffc619;
            z-index: 1000;
        }

        .highlight {
            color: #47ebeb;
            font-weight: bold;
        }

        /* 响应式调整 */
        @media (max-aspect-ratio: 4/3) {
            .header h1 { font-size: 2.5vw; }
            .zoom-info { font-size: 1.2vw; }
            .test-btn { font-size: 1vw; }
            .frame-header { font-size: 1.2vw; }
            .instructions { font-size: 1vw; }
        }

        @media (min-aspect-ratio: 21/9) {
            .header h1 { font-size: 1.2vw; }
            .zoom-info { font-size: 0.6vw; }
            .test-btn { font-size: 0.5vw; }
            .frame-header { font-size: 0.6vw; }
            .instructions { font-size: 0.5vw; }
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
</head>
<body>
    <div class="comparison-container">
        <div class="header">
            <h1>🎯 缩放效果对比测试 - 像素单位 vs 视口单位 (vw/vh)</h1>
        </div>

        <div class="controls">
            <div class="zoom-info" id="zoomInfo">
                当前缩放: <span id="zoomLevel">100%</span> | 视口: <span id="viewport">1920x1080</span>
            </div>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="setZoom(0.5)">50% 缩放</button>
                <button class="test-btn" onclick="setZoom(0.75)">75% 缩放</button>
                <button class="test-btn" onclick="setZoom(1.0)">100% 缩放</button>
                <button class="test-btn" onclick="setZoom(1.25)">125% 缩放</button>
                <button class="test-btn" onclick="setZoom(1.5)">150% 缩放</button>
                <button class="test-btn" onclick="setZoom(2.0)">200% 缩放</button>
            </div>
        </div>

        <div class="frames-container">
            <div class="frame">
                <div class="frame-header">
                    <i class="bi bi-exclamation-triangle"></i> 
                    原版本 (像素单位) - 缩放时比例不一致
                </div>
                <div class="frame-content">
                    <iframe id="originalFrame" src="index.html"></iframe>
                </div>
            </div>

            <div class="frame">
                <div class="frame-header">
                    <i class="bi bi-check-circle"></i> 
                    优化版本 (vw/vh单位) - <span class="highlight">完美缩放一致性</span>
                </div>
                <div class="frame-content">
                    <iframe id="optimizedFrame" src="viewport-test.html"></iframe>
                </div>
            </div>
        </div>

        <div class="instructions">
            <strong>测试说明：</strong>
            1. 点击上方缩放按钮或使用 <span class="highlight">Ctrl + 滚轮</span> 缩放页面
            2. 观察两个版本在不同缩放比例下的显示效果
            3. <span class="highlight">右侧版本</span> 使用 vw/vh 单位，在任何缩放比例下都保持完美的视觉一致性
            4. <span class="highlight">左侧版本</span> 使用传统像素单位，缩放时会出现比例不协调的问题
        </div>
    </div>

    <script>
        function updateZoomInfo() {
            const zoomLevel = document.getElementById('zoomLevel');
            const viewport = document.getElementById('viewport');
            
            // 计算缩放比例
            const zoom = Math.round((window.outerWidth / window.innerWidth) * 100);
            const vw = window.innerWidth;
            const vh = window.innerHeight;
            
            zoomLevel.textContent = zoom + '%';
            viewport.textContent = vw + 'x' + vh;
        }

        function setZoom(scale) {
            document.body.style.zoom = scale;
            setTimeout(updateZoomInfo, 100);
        }

        // 初始化
        updateZoomInfo();

        // 监听窗口变化
        window.addEventListener('resize', updateZoomInfo);
        
        // 监听缩放变化
        let resizeTimer;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(updateZoomInfo, 100);
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1': setZoom(0.5); e.preventDefault(); break;
                    case '2': setZoom(0.75); e.preventDefault(); break;
                    case '3': setZoom(1.0); e.preventDefault(); break;
                    case '4': setZoom(1.25); e.preventDefault(); break;
                    case '5': setZoom(1.5); e.preventDefault(); break;
                    case '6': setZoom(2.0); e.preventDefault(); break;
                }
            }
        });

        console.log('🎯 缩放效果对比测试页面已加载');
        console.log('📋 快捷键：Ctrl + 1-6 快速切换缩放比例');
        console.log('🔍 观察重点：右侧 vw/vh 版本在任何缩放下都保持完美比例');
    </script>
</body>
</html>
